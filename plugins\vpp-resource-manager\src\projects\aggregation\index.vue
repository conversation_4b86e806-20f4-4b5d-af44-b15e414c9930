<template>
  <div class="aggregation-container">
    <!-- 搜索和筛选区域 -->
    <div class="search-filter-section">
      <div class="search-row">
        <!-- 关键字搜索 -->
        <ElInput
          class="search-input"
          v-model="ElInput_search.value"
          v-bind="ElInput_search"
          v-on="ElInput_search.event"
          placeholder="请输入关键字"
          suffix-icon="el-icon-search"
        />

        <!-- 机组类型下拉选择 -->
        <CustomElSelect
          class="type-select"
          v-model="ElSelect_unitType.value"
          v-bind="ElSelect_unitType"
          v-on="ElSelect_unitType.event"
          :prefix_in="$T('机组类型')"
        >
          <ElOption
            v-for="item in ElOption_unitType.options_in"
            :key="item[ElOption_unitType.key]"
            :label="item[ElOption_unitType.label]"
            :value="item[ElOption_unitType.value]"
            :disabled="item[ElOption_unitType.disabled]"
          />
        </CustomElSelect>

        <!-- 新增按钮 -->
        <CetButton
          class="add-button"
          v-bind="CetButton_add"
          v-on="CetButton_add.event"
        />
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <CetTable
        :data.sync="CetTable_units.data"
        :dynamicInput.sync="CetTable_units.dynamicInput"
        v-bind="CetTable_units"
        v-on="CetTable_units.event"
        :border="false"
      >
        <!-- 序号列 -->
        <ElTableColumn
          type="index"
          label="序号"
          width="72"
          align="center"
          header-align="center"
        />

        <!-- 机组名称列 -->
        <ElTableColumn v-bind="ElTableColumn_unitName" />

        <!-- 机组类型列 -->
        <ElTableColumn v-bind="ElTableColumn_unitType" />

        <!-- 聚合资源数量列 -->
        <ElTableColumn v-bind="ElTableColumn_resourceCount" />

        <!-- 操作列 -->
        <ElTableColumn v-bind="ElTableColumn_operations">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row)"
              class="danger-text"
            >
              删除
            </el-button>
            <el-button type="text" size="small" @click="handleView(scope.row)">
              详情
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
          </template>
        </ElTableColumn>
      </CetTable>

      <!-- 分页器 -->
      <el-pagination
        class="pagination-container"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 15, 20, 50, 100]"
        :page-size="pageSize"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
      />
    </div>
  </div>
</template>

<script>
import { CustomElSelect } from "eem-base/components";

export default {
  name: "AggregationUnitList",
  components: {
    CustomElSelect
  },
  data() {
    return {
      // 搜索输入框
      ElInput_search: {
        value: "",
        clearable: true,
        event: {
          input: this._.debounce(this.handleSearch, 300),
          clear: this.handleSearch
        }
      },

      // 机组类型选择器
      ElSelect_unitType: {
        value: "",
        clearable: true,
        event: {
          change: this.handleTypeChange
        }
      },

      // 机组类型选项
      ElOption_unitType: {
        options_in: [
          { id: "", name: "全部" },
          { id: "thermal", name: "火电机组" },
          { id: "hydro", name: "水电机组" },
          { id: "wind", name: "风电机组" },
          { id: "solar", name: "光伏机组" },
          { id: "nuclear", name: "核电机组" },
          { id: "gas", name: "燃气机组" }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },

      // 新增按钮
      CetButton_add: {
        visible_in: true,
        disable_in: false,
        title: "新增",
        type: "primary",
        event: {
          statusTrigger_out: this.handleAdd
        }
      },

      // 机组列表表格
      CetTable_units: {
        queryMode: "trigger",
        dataMode: "component", // 使用组件数据模式
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "units",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "unitName_in", operator: "LIKE", prop: "unitName" },
            { name: "unitType_in", operator: "EQ", prop: "unitType" }
          ],
          hasQueryNode: false
        },
        data: [],
        dynamicInput: {
          unitName_in: "",
          unitType_in: ""
        },
        queryTrigger_in: new Date().getTime(),
        showPagination: false, // 禁用内置分页器
        event: {
          outputData_out: this.CetTable_units_outputData_out
        }
      },

      // 分页相关数据
      allUnitsData: [], // 存储所有数据
      filteredData: [], // 存储过滤后的数据
      currentPage: 1,
      pageSize: 15,
      total: 0,

      // 表格列配置
      ElTableColumn_unitName: {
        prop: "unitName",
        label: "机组名称",
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true
      },

      ElTableColumn_unitType: {
        prop: "unitTypeName",
        label: "机组类型",
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true
      },

      ElTableColumn_resourceCount: {
        prop: "resourceCount",
        label: "聚合资源数量",
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        formatter: row => {
          return row.resourceCount ? `${row.resourceCount}个` : "0个";
        }
      },

      ElTableColumn_operations: {
        label: "操作",
        headerAlign: "center",
        align: "center",
        width: "180",
        fixed: "right"
      }
    };
  },

  mounted() {
    // 初始化数据
    this.allUnitsData = this.generateMockData();
    this.filteredData = [...this.allUnitsData];
    this.total = this.filteredData.length;
    // 初始化分页数据
    this.updateTableData();
  },

  methods: {
    // 生成模拟数据
    generateMockData() {
      const unitTypes = [
        { id: "thermal", name: "火电机组" },
        { id: "hydro", name: "水电机组" },
        { id: "wind", name: "风电机组" },
        { id: "solar", name: "光伏机组" },
        { id: "nuclear", name: "核电机组" },
        { id: "gas", name: "燃气机组" }
      ];

      const data = [];
      for (let i = 1; i <= 150; i++) {
        const unitType =
          unitTypes[Math.floor(Math.random() * unitTypes.length)];

        data.push({
          id: i,
          unitName: `${i}#调峰机组`,
          unitType: unitType.id,
          unitTypeName: unitType.name,
          resourceCount: Math.floor(Math.random() * 50) + 5 // 5-54个聚合资源
        });
      }

      return data;
    },

    // 更新表格数据（分页处理）
    updateTableData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      this.CetTable_units.data = this.filteredData.slice(start, end);
    },

    // 过滤数据
    filterData() {
      const searchKeyword = this.ElInput_search.value.toLowerCase();
      const selectedType = this.ElSelect_unitType.value;

      this.filteredData = this.allUnitsData.filter(item => {
        // 名称过滤
        const nameMatch =
          !searchKeyword || item.unitName.toLowerCase().includes(searchKeyword);

        // 类型过滤
        const typeMatch = !selectedType || item.unitType === selectedType;

        return nameMatch && typeMatch;
      });

      // 重置分页
      this.currentPage = 1;
      this.total = this.filteredData.length;

      // 更新表格数据
      this.updateTableData();
    },

    // 搜索处理
    handleSearch() {
      this.CetTable_units.dynamicInput.unitName_in = this.ElInput_search.value;
      this.filterData();
    },

    // 类型变化处理
    handleTypeChange() {
      this.CetTable_units.dynamicInput.unitType_in =
        this.ElSelect_unitType.value;
      this.filterData();
    },

    // 分页大小变化处理
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.currentPage = 1; // 重置到第一页
      this.updateTableData();
    },

    // 当前页变化处理
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.updateTableData();
    },

    // 新增按钮处理
    handleAdd() {
      this.$message.success("新增机组功能待实现");
    },

    // 编辑处理
    handleEdit(row) {
      this.$message.info(`编辑机组: ${row.unitName}`);
    },

    // 详情处理
    handleView(row) {
      this.$message.info(`查看机组详情: ${row.unitName}`);
    },

    // 删除处理
    handleDelete(row) {
      this.$confirm(`确定要删除机组 "${row.unitName}" 吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$message.success("删除成功");
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },

    // 表格输出数据处理
    CetTable_units_outputData_out(val) {
      console.log("表格输出数据:", val);
    }
  }
};
</script>

<style scoped>
.aggregation-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--BG1);
  overflow: auto;
}

.search-filter-section {
  padding: 24px;
}

.search-row {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-input {
  width: 240px;
}

.type-select {
  width: 240px;
}

.add-button {
  margin-left: auto;
}

.table-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 24px 24px 24px;
  overflow: hidden;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.unit-table {
  flex: 1;
  overflow: auto;
}

/* 移除表格列分割线 */
.unit-table :deep(.el-table) {
  border: none;
  height: 100%;
}

.unit-table :deep(.el-table__body-wrapper) {
  overflow: auto;
}

.danger-text {
  color: var(--Sta3);
}

.danger-text:hover {
  color: var(--Sta3);
}
</style>
